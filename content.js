// Check if extension runtime is valid before sending message
let selectedTextCache = '';
const createFloatingButton = () => {
  const button = document.createElement('div');
  button.style.position = 'fixed';
  button.style.zIndex = '9999';
  button.style.backgroundColor = '#4285f4';
  button.style.color = 'white';
  button.style.padding = '5px 10px';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  button.textContent = 'Format Text';
  
  button.addEventListener('click', () => {
    if (selectedTextCache) {
      chrome.runtime.sendMessage({ action: "formatText", text: selectedTextCache });
    }
    document.body.removeChild(button);
  });
  
  return button;
};

const handleTextSelection = () => {
  try {
    // Remove existing buttons
    document.querySelectorAll('.floating-format-button').forEach(btn => btn.remove());
    
    // Skip if on restricted page (like SQL Editor)
    if (window.location.href.includes('rds.mws.sankuai.com/dba/sqlEditor')) {
      return;
    }
    
    if (typeof chrome === 'undefined' || !chrome.runtime?.id) {
      console.error('Extension context invalidated - Chrome API not available');
      return;
    }
    
    selectedTextCache = window.getSelection().toString().trim();
    if (selectedTextCache) {
      const button = createFloatingButton();
      button.className = 'floating-format-button';
      
      const range = window.getSelection().getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      button.style.top = `${rect.bottom + window.scrollY + 5}px`;
      button.style.left = `${rect.left + window.scrollX}px`;
      
      document.body.appendChild(button);
    }
  } catch (e) {
    console.error('Error in content.js:', e);
  }
};

// Use passive event listener for performance
document.addEventListener('mouseup', handleTextSelection, { passive: true });