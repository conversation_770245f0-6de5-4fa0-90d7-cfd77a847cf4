// Handle both direct and button-triggered formatting
chrome.runtime.onMessage.addListener((request) => {
  if (request.action === "formatText" && request.text) {
    try {
      const parsed = JSON.parse(request.text);
      document.getElementById('formattedText').textContent = JSON.stringify(parsed, null, 2);
    } catch (e) {
      document.getElementById('formattedText').textContent = request.text;
    }
  }
});