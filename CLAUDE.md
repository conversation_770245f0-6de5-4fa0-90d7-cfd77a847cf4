# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## rules
永远用中文回复我

## Commands
- **Build**: No build command required (Chrome extension).
- **Lint**: No linting configuration found.
- **Run Tests**: No test framework detected.

## Architecture
- **Extension Structure**:
  - `manifest.json`: Defines the extension's metadata and permissions.
  - `popup.js`: Handles text formatting logic in the popup.
  - `content.js`: Listens for text selection and sends it to the popup for formatting.
  - `popup.html`: The popup UI.
- **Workflow**:
  - Text is selected on a webpage, captured by `content.js`, and sent to `popup.js` for formatting (e.g., JSON).
  - The formatted text is displayed in the popup.